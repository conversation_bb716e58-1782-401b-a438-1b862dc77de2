import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_profile.dart';

class UserProfileService {
  static const String _userProfileKey = 'user_profile';
  static UserProfileService? _instance;
  
  SharedPreferences? _prefs;
  UserProfile? _currentProfile;

  UserProfileService._internal();

  static Future<UserProfileService> getInstance() async {
    _instance ??= UserProfileService._internal();
    await _instance!._initialize();
    return _instance!;
  }

  Future<void> _initialize() async {
    if (_prefs == null) {
      _prefs = await SharedPreferences.getInstance();
      await _loadProfile();
    }
  }

  Future<void> _loadProfile() async {
    try {
      final profileData = _prefs!.getString(_userProfileKey);
      if (profileData != null) {
        final profileJson = jsonDecode(profileData);
        _currentProfile = UserProfile.fromJson(profileJson);
      } else {
        // 如果没有保存的配置，创建默认配置
        _currentProfile = UserProfile.defaultProfile();
        await _saveProfile(_currentProfile!);
      }
    } catch (e) {
      // 如果加载失败，使用默认配置
      _currentProfile = UserProfile.defaultProfile();
      await _saveProfile(_currentProfile!);
    }
  }

  Future<void> _saveProfile(UserProfile profile) async {
    try {
      final profileJson = jsonEncode(profile.toJson());
      await _prefs!.setString(_userProfileKey, profileJson);
      _currentProfile = profile;
    } catch (e) {
      throw Exception('保存用户配置失败: $e');
    }
  }

  // 获取当前用户配置
  UserProfile getCurrentProfile() {
    return _currentProfile ?? UserProfile.defaultProfile();
  }

  // 更新昵称
  Future<void> updateNickname(String nickname) async {
    if (nickname.trim().isEmpty) {
      throw Exception('昵称不能为空');
    }
    
    final updatedProfile = _currentProfile!.copyWith(
      nickname: nickname.trim(),
      updatedAt: DateTime.now(),
    );
    
    await _saveProfile(updatedProfile);
  }

  // 更新头像
  Future<void> updateAvatar(String avatarPath) async {
    if (avatarPath.trim().isEmpty) {
      throw Exception('头像路径不能为空');
    }
    
    final updatedProfile = _currentProfile!.copyWith(
      avatarPath: avatarPath.trim(),
      updatedAt: DateTime.now(),
    );
    
    await _saveProfile(updatedProfile);
  }

  // 更新语言设置
  Future<void> updateLanguage(String language) async {
    if (language.trim().isEmpty) {
      throw Exception('语言设置不能为空');
    }

    final updatedProfile = _currentProfile!.copyWith(
      language: language.trim(),
      updatedAt: DateTime.now(),
    );

    await _saveProfile(updatedProfile);
  }

  // 更新性别设置
  Future<void> updateGender(Gender gender) async {
    final updatedProfile = _currentProfile!.copyWith(
      gender: gender,
      updatedAt: DateTime.now(),
    );

    await _saveProfile(updatedProfile);
  }

  // 重置为默认配置
  Future<void> resetToDefault() async {
    final defaultProfile = UserProfile.defaultProfile();
    await _saveProfile(defaultProfile);
  }

  // 清除所有用户数据
  Future<void> clearUserData() async {
    await _prefs!.remove(_userProfileKey);
    _currentProfile = UserProfile.defaultProfile();
  }

  // 获取应用版本信息（这里可以从package_info_plus获取）
  String getAppVersion() {
    // TODO: 集成package_info_plus获取真实版本信息
    return '1.0.0';
  }

  // 获取预定义头像列表
  List<String> getAvailableAvatars() {
    // 返回预定义头像路径列表
    return [
      'assets/avatars/avatar_1.png',
      'assets/avatars/avatar_2.png',
      'assets/avatars/avatar_3.png',
      'assets/avatars/avatar_4.png',
      'assets/avatars/avatar_5.png',
      'assets/avatars/avatar_6.png',
      'assets/avatars/avatar_7.png',
      'assets/avatars/avatar_8.png',
      'assets/avatars/avatar_9.png',
      'assets/avatars/avatar_10.png',
      'assets/avatars/avatar_11.png',
      'assets/avatars/avatar_12.png',
    ];
  }

  // 获取男性头像列表
  List<String> getMaleAvatars() {
    return [
      'assets/avatars/male_1.png',
      'assets/avatars/male_2.png',
      'assets/avatars/male_3.png',
      'assets/avatars/male_4.png',
      'assets/avatars/male_5.png',
      'assets/avatars/male_6.png',
    ];
  }

  // 获取女性头像列表
  List<String> getFemaleAvatars() {
    return [
      'assets/avatars/female_1.png',
      'assets/avatars/female_2.png',
      'assets/avatars/female_3.png',
      'assets/avatars/female_4.png',
      'assets/avatars/female_5.png',
      'assets/avatars/female_6.png',
    ];
  }

  // 根据头像路径推断性别
  Gender getGenderFromAvatarPath(String avatarPath) {
    if (avatarPath.contains('male_')) {
      return Gender.male;
    } else if (avatarPath.contains('female_')) {
      return Gender.female;
    }
    return Gender.unspecified;
  }

  // 更新头像和性别
  Future<void> updateAvatarAndGender(String avatarPath) async {
    if (avatarPath.trim().isEmpty) {
      throw Exception('头像路径不能为空');
    }

    final gender = getGenderFromAvatarPath(avatarPath);
    final updatedProfile = _currentProfile!.copyWith(
      avatarPath: avatarPath.trim(),
      gender: gender,
      updatedAt: DateTime.now(),
    );

    await _saveProfile(updatedProfile);
  }
}
