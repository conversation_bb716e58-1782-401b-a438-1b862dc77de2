import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../ui/design_spec.dart';

class LanguageSettingsPage extends StatefulWidget {
  const LanguageSettingsPage({super.key});

  @override
  State<LanguageSettingsPage> createState() => _LanguageSettingsPageState();
}

class _LanguageSettingsPageState extends State<LanguageSettingsPage> {
  String _selectedLanguage = 'en';

  final Map<String, String> _languages = {
    'en': 'English',
    'zh': '繁體中文',
    'ar': 'العربية',
    'hi': 'हिंदी',
  };

  @override
  void initState() {
    super.initState();
    // 从当前上下文获取语言设置
    final locale = Localizations.localeOf(context);
    _selectedLanguage = locale.languageCode;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSpec.primaryBackground,
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.languageSettings),
        backgroundColor: DesignSpec.primaryBackground,
        elevation: 0,
        foregroundColor: DesignSpec.primaryItemSelected,
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context, _selectedLanguage);
            },
            child: Text(
              AppLocalizations.of(context)!.confirm,
              style: TextStyle(
                color: DesignSpec.primaryItemSelected,
                fontWeight: DesignSpec.fontWeightMedium,
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Container(
            decoration: BoxDecoration(
              color: DesignSpec.secondaryBackground,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Icon(
                        Icons.language,
                        size: 32,
                        color: DesignSpec.primaryItemSelected,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.languageSettings,
                              style: TextStyle(
                                fontSize: DesignSpec.fontSizeLg,
                                fontWeight: DesignSpec.fontWeightMedium,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '选择您偏好的应用语言',
                              style: TextStyle(
                                fontSize: DesignSpec.fontSizeSm,
                                color: DesignSpec.primaryItemUnselected,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const Divider(height: 1),
                ..._languages.entries.map((entry) {
                  return _buildLanguageOption(entry.key, entry.value);
                }).toList(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageOption(String languageCode, String languageName) {
    final isSelected = languageCode == _selectedLanguage;
    
    return InkWell(
      onTap: () {
        setState(() {
          _selectedLanguage = languageCode;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected 
                      ? DesignSpec.primaryItemSelected 
                      : DesignSpec.primaryItemUnselected.withOpacity(0.5),
                  width: 2,
                ),
              ),
              child: isSelected
                  ? Center(
                      child: Container(
                        width: 10,
                        height: 10,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: DesignSpec.primaryItemSelected,
                        ),
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                languageName,
                style: TextStyle(
                  fontSize: DesignSpec.fontSizeBase,
                  fontWeight: isSelected 
                      ? DesignSpec.fontWeightMedium 
                      : DesignSpec.fontWeightNormal,
                  color: isSelected 
                      ? DesignSpec.primaryItemSelected 
                      : Colors.black87,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check,
                color: DesignSpec.primaryItemSelected,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }
}
